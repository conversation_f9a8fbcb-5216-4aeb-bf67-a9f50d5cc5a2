from django.contrib import admin
from django.urls import include, path

from Project.views import AppleFileView
from core import settings
from django.conf.urls.static import static

def trigger_error(request):
    division_by_zero = 1 / 0


urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include('Authentication.urls')),
    path('api/', include('Project.urls')),
    path('api/', include('google_business.urls')),
    path('.well-known/',include('Project.urls'))
]+ static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
