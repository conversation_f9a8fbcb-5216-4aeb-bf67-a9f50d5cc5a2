from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json
from .google_business_auth import (
    get_google_business_auth_url,
    exchange_google_business_code,
    refresh_google_business_token
)
from .get_business_profile import (
    get_google_business_accounts,
    get_google_business_locations,
    get_google_business_location_details
)
from .google_business_analytics import (
    get_google_business_insights,
    get_google_business_performance,
    get_google_business_reviews,
    get_google_business_photos
)

@csrf_exempt
@require_http_methods(["POST"])
def google_business_auth_url(request):
    """
    Generate Google Business Profile OAuth URL
    """
    try:
        data = json.loads(request.body)
        client_id = data.get('client_id')
        client_secret = data.get('client_secret')
        
        if not client_id or not client_secret:
            return JsonResponse({
                'error': 'client_id and client_secret are required',
                'success': False
            }, status=400)
        
        result = get_google_business_auth_url(client_id, client_secret)
        
        if result.get('success'):
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data',
            'success': False
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'Server error: {str(e)}',
            'success': False
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def google_business_callback(request):
    """
    Handle Google Business Profile OAuth callback
    """
    try:
        data = json.loads(request.body)
        code = data.get('code')
        client_id = data.get('client_id')
        client_secret = data.get('client_secret')
        state = data.get('state')
        
        if not all([code, client_id, client_secret]):
            return JsonResponse({
                'error': 'code, client_id, and client_secret are required',
                'success': False
            }, status=400)
        
        result = exchange_google_business_code(code, client_id, client_secret, state)
        
        if result.get('success'):
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data',
            'success': False
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'Server error: {str(e)}',
            'success': False
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def google_business_refresh_token(request):
    """
    Refresh Google Business Profile access token
    """
    try:
        data = json.loads(request.body)
        refresh_token = data.get('refresh_token')
        client_id = data.get('client_id')
        client_secret = data.get('client_secret')
        
        if not all([refresh_token, client_id, client_secret]):
            return JsonResponse({
                'error': 'refresh_token, client_id, and client_secret are required',
                'success': False
            }, status=400)
        
        result = refresh_google_business_token(refresh_token, client_id, client_secret)
        
        if result.get('success'):
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data',
            'success': False
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'Server error: {str(e)}',
            'success': False
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def google_business_accounts(request):
    """
    Get Google Business Profile accounts
    """
    try:
        data = json.loads(request.body)
        access_token = data.get('access_token')
        
        if not access_token:
            return JsonResponse({
                'error': 'access_token is required',
                'success': False
            }, status=400)
        
        result = get_google_business_accounts(access_token)
        
        if result.get('success'):
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data',
            'success': False
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'Server error: {str(e)}',
            'success': False
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def google_business_locations(request):
    """
    Get Google Business Profile locations
    """
    try:
        data = json.loads(request.body)
        access_token = data.get('access_token')
        account_id = data.get('account_id')  # Optional
        
        if not access_token:
            return JsonResponse({
                'error': 'access_token is required',
                'success': False
            }, status=400)
        
        result = get_google_business_locations(access_token, account_id)
        
        if result.get('success'):
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data',
            'success': False
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'Server error: {str(e)}',
            'success': False
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def google_business_location_details(request):
    """
    Get detailed information for a specific location
    """
    try:
        data = json.loads(request.body)
        access_token = data.get('access_token')
        location_name = data.get('location_name')
        
        if not all([access_token, location_name]):
            return JsonResponse({
                'error': 'access_token and location_name are required',
                'success': False
            }, status=400)
        
        result = get_google_business_location_details(access_token, location_name)
        
        if result.get('success'):
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data',
            'success': False
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'Server error: {str(e)}',
            'success': False
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def google_business_insights(request):
    """
    Get Google Business Profile insights/analytics
    """
    try:
        data = json.loads(request.body)
        access_token = data.get('access_token')
        location_name = data.get('location_name')
        start_date = data.get('start_date')  # Optional
        end_date = data.get('end_date')      # Optional
        
        if not all([access_token, location_name]):
            return JsonResponse({
                'error': 'access_token and location_name are required',
                'success': False
            }, status=400)
        
        result = get_google_business_insights(access_token, location_name, start_date, end_date)
        
        if result.get('success'):
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data',
            'success': False
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'Server error: {str(e)}',
            'success': False
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def google_business_performance(request):
    """
    Get Google Business Profile performance metrics
    """
    try:
        data = json.loads(request.body)
        access_token = data.get('access_token')
        location_name = data.get('location_name')
        metric_type = data.get('metric_type', 'all')
        start_date = data.get('start_date')  # Optional
        end_date = data.get('end_date')      # Optional
        
        if not all([access_token, location_name]):
            return JsonResponse({
                'error': 'access_token and location_name are required',
                'success': False
            }, status=400)
        
        result = get_google_business_performance(access_token, location_name, metric_type, start_date, end_date)
        
        if result.get('success'):
            return JsonResponse(result)
        else:
            return JsonResponse(result, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data',
            'success': False
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'Server error: {str(e)}',
            'success': False
        }, status=500)
