#!/usr/bin/env python3
"""
Test script for Google Business Profile integration
Run this script to test the API endpoints
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:8000/api"
CLIENT_ID = "your_google_client_id_here"
CLIENT_SECRET = "your_google_client_secret_here"

def test_auth_url():
    """Test getting authorization URL"""
    print("🔐 Testing Google Business Auth URL generation...")
    
    response = requests.post(f"{BASE_URL}/google-business/auth-url/", json={
        "client_id": CLIENT_ID,
        "client_secret": CLIENT_SECRET
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Auth URL generated successfully!")
        print(f"📋 Authorization URL: {data.get('authorization_url', '')[:100]}...")
        return data.get('state')
    else:
        print(f"❌ Failed to generate auth URL: {response.text}")
        return None

def test_callback(code, state):
    """Test OAuth callback (requires manual code input)"""
    print("🔄 Testing OAuth callback...")
    
    response = requests.post(f"{BASE_URL}/google-business/callback/", json={
        "code": code,
        "client_id": CLIENT_ID,
        "client_secret": CLIENT_SECRET,
        "state": state
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Token exchange successful!")
        print(f"📋 Access Token: {data.get('access_token', '')[:20]}...")
        return data.get('access_token')
    else:
        print(f"❌ Token exchange failed: {response.text}")
        return None

def test_accounts(access_token):
    """Test getting business accounts"""
    print("🏢 Testing Google Business Accounts retrieval...")
    
    response = requests.post(f"{BASE_URL}/google-business/accounts/", json={
        "access_token": access_token
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Accounts retrieved successfully!")
        print(f"📋 Total accounts: {data.get('total_accounts', 0)}")
        if data.get('accounts'):
            print(f"📋 First account: {data['accounts'][0].get('account_name', 'N/A')}")
            return data['accounts'][0].get('account_id')
    else:
        print(f"❌ Failed to get accounts: {response.text}")
    return None

def test_locations(access_token, account_id=None):
    """Test getting business locations"""
    print("📍 Testing Google Business Locations retrieval...")
    
    payload = {"access_token": access_token}
    if account_id:
        payload["account_id"] = account_id
    
    response = requests.post(f"{BASE_URL}/google-business/locations/", json=payload)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Locations retrieved successfully!")
        print(f"📋 Total locations: {data.get('total_locations', 0)}")
        if data.get('locations'):
            location = data['locations'][0]
            print(f"📋 First location: {location.get('business_name', 'N/A')}")
            return location.get('full_name')
    else:
        print(f"❌ Failed to get locations: {response.text}")
    return None

def test_insights(access_token, location_name):
    """Test getting business insights"""
    print("📊 Testing Google Business Insights retrieval...")
    
    response = requests.post(f"{BASE_URL}/google-business/insights/", json={
        "access_token": access_token,
        "location_name": location_name,
        "start_date": "2024-01-01",
        "end_date": "2024-01-31"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Insights retrieved successfully!")
        metrics = data.get('metrics', {})
        views = metrics.get('views', {})
        actions = metrics.get('actions', {})
        print(f"📋 Total views: {views.get('total_views', 0)}")
        print(f"📋 Total actions: {actions.get('total_actions', 0)}")
    else:
        print(f"❌ Failed to get insights: {response.text}")

def test_performance(access_token, location_name):
    """Test getting performance metrics"""
    print("📈 Testing Google Business Performance metrics...")
    
    response = requests.post(f"{BASE_URL}/google-business/performance/", json={
        "access_token": access_token,
        "location_name": location_name,
        "metric_type": "all",
        "start_date": "2024-01-01",
        "end_date": "2024-01-31"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Performance metrics retrieved successfully!")
        overview = data.get('overview', {})
        print(f"📋 Total views: {overview.get('total_views', 0)}")
        print(f"📋 Total actions: {overview.get('total_actions', 0)}")
        print(f"📋 View growth: {overview.get('view_growth', 0)}%")
    else:
        print(f"❌ Failed to get performance metrics: {response.text}")

def main():
    """Main test function"""
    print("🚀 Starting Google Business Profile Integration Tests")
    print("=" * 60)
    
    # Check if client credentials are set
    if CLIENT_ID == "your_google_client_id_here" or CLIENT_SECRET == "your_google_client_secret_here":
        print("❌ Please update CLIENT_ID and CLIENT_SECRET in the script")
        print("📋 Get credentials from: https://console.cloud.google.com/")
        return
    
    # Test 1: Get authorization URL
    state = test_auth_url()
    if not state:
        return
    
    print("\n" + "=" * 60)
    print("📋 MANUAL STEP REQUIRED:")
    print("1. Visit the authorization URL above")
    print("2. Complete the OAuth flow")
    print("3. Copy the 'code' parameter from the callback URL")
    print("4. Enter it below when prompted")
    print("=" * 60)
    
    # Get authorization code from user
    code = input("\n🔑 Enter the authorization code: ").strip()
    if not code:
        print("❌ No authorization code provided")
        return
    
    # Test 2: Exchange code for token
    access_token = test_callback(code, state)
    if not access_token:
        return
    
    print("\n" + "-" * 40)
    
    # Test 3: Get accounts
    account_id = test_accounts(access_token)
    
    print("\n" + "-" * 40)
    
    # Test 4: Get locations
    location_name = test_locations(access_token, account_id)
    if not location_name:
        print("❌ Cannot proceed without location data")
        return
    
    print("\n" + "-" * 40)
    
    # Test 5: Get insights
    test_insights(access_token, location_name)
    
    print("\n" + "-" * 40)
    
    # Test 6: Get performance metrics
    test_performance(access_token, location_name)
    
    print("\n" + "=" * 60)
    print("🎉 Google Business Profile Integration Tests Completed!")
    print("=" * 60)

if __name__ == "__main__":
    main()
