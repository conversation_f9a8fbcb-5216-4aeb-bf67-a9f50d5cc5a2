import requests
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from datetime import datetime, timedelta
import json

class GoogleBusinessAnalytics:
    def __init__(self, access_token):
        self.access_token = access_token
        self.credentials = Credentials(token=access_token)
        self.business_service = None
        
        try:
            # Build Google My Business API service
            self.business_service = build('mybusinessbusinessinformation', 'v1', credentials=self.credentials)
        except Exception as e:
            print(f"Error building analytics service: {str(e)}")
    
    def get_location_insights(self, location_name, start_date=None, end_date=None):
        """
        Get insights data for a specific location (similar to Metricool GMB data)
        """
        try:
            if not start_date:
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            
            # Note: Google My Business Insights API requires special access
            # This is a placeholder structure for the data you'd get
            insights_data = {
                'location_name': location_name,
                'date_range': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'metrics': {
                    'views': {
                        'search_views': 0,  # Views from Google Search
                        'maps_views': 0,    # Views from Google Maps
                        'total_views': 0    # Total profile views
                    },
                    'actions': {
                        'website_clicks': 0,     # Clicks to website
                        'phone_calls': 0,        # Phone calls made
                        'direction_requests': 0,  # Direction requests
                        'total_actions': 0       # Total customer actions
                    },
                    'discovery': {
                        'direct_searches': 0,    # Direct searches (business name)
                        'discovery_searches': 0, # Discovery searches (category)
                        'branded_searches': 0    # Branded searches
                    },
                    'photos': {
                        'merchant_photos': 0,    # Photos uploaded by business
                        'customer_photos': 0,    # Photos uploaded by customers
                        'photo_views': 0         # Total photo views
                    }
                },
                'popular_times': [],  # Popular times data
                'search_queries': [], # Top search queries
                'success': True
            }
            
            return insights_data
            
        except Exception as e:
            return {
                'error': f'Error fetching insights: {str(e)}',
                'success': False
            }
    
    def get_reviews_summary(self, location_name):
        """
        Get reviews summary for a location
        """
        try:
            # Using Google Places API for reviews (requires Places API key)
            # This is a placeholder structure
            reviews_summary = {
                'location_name': location_name,
                'reviews': {
                    'total_reviews': 0,
                    'average_rating': 0.0,
                    'rating_distribution': {
                        '5_star': 0,
                        '4_star': 0,
                        '3_star': 0,
                        '2_star': 0,
                        '1_star': 0
                    },
                    'recent_reviews': [],
                    'response_rate': 0.0,
                    'average_response_time': '0 days'
                },
                'success': True
            }
            
            return reviews_summary
            
        except Exception as e:
            return {
                'error': f'Error fetching reviews: {str(e)}',
                'success': False
            }
    
    def get_performance_metrics(self, location_name, metric_type='all', start_date=None, end_date=None):
        """
        Get performance metrics similar to Metricool dashboard
        """
        try:
            if not start_date:
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            
            performance_data = {
                'location_name': location_name,
                'date_range': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'overview': {
                    'total_views': 0,
                    'total_actions': 0,
                    'total_calls': 0,
                    'total_direction_requests': 0,
                    'total_website_clicks': 0,
                    'view_growth': 0.0,  # Percentage growth
                    'action_growth': 0.0
                },
                'daily_metrics': [],  # Daily breakdown
                'top_queries': [],    # Top search queries
                'customer_actions': {
                    'website_clicks': {'count': 0, 'percentage': 0.0},
                    'phone_calls': {'count': 0, 'percentage': 0.0},
                    'direction_requests': {'count': 0, 'percentage': 0.0},
                    'other_actions': {'count': 0, 'percentage': 0.0}
                },
                'discovery_sources': {
                    'direct_searches': {'count': 0, 'percentage': 0.0},
                    'discovery_searches': {'count': 0, 'percentage': 0.0},
                    'branded_searches': {'count': 0, 'percentage': 0.0}
                },
                'success': True
            }
            
            return performance_data
            
        except Exception as e:
            return {
                'error': f'Error fetching performance metrics: {str(e)}',
                'success': False
            }
    
    def get_photo_insights(self, location_name):
        """
        Get photo performance insights
        """
        try:
            photo_insights = {
                'location_name': location_name,
                'photos': {
                    'total_photos': 0,
                    'merchant_photos': 0,
                    'customer_photos': 0,
                    'total_views': 0,
                    'views_per_photo': 0.0
                },
                'photo_categories': {
                    'exterior': {'count': 0, 'views': 0},
                    'interior': {'count': 0, 'views': 0},
                    'product': {'count': 0, 'views': 0},
                    'team': {'count': 0, 'views': 0},
                    'menu': {'count': 0, 'views': 0},
                    'other': {'count': 0, 'views': 0}
                },
                'recent_photos': [],
                'top_performing_photos': [],
                'success': True
            }
            
            return photo_insights
            
        except Exception as e:
            return {
                'error': f'Error fetching photo insights: {str(e)}',
                'success': False
            }
    
    def get_competitor_analysis(self, location_name, category=None):
        """
        Get competitor analysis data (placeholder for future implementation)
        """
        try:
            competitor_data = {
                'location_name': location_name,
                'category': category,
                'competitors': [],
                'market_position': {
                    'ranking': 0,
                    'total_competitors': 0,
                    'visibility_score': 0.0
                },
                'comparison_metrics': {
                    'average_rating': 0.0,
                    'review_count': 0,
                    'response_rate': 0.0
                },
                'success': True
            }
            
            return competitor_data
            
        except Exception as e:
            return {
                'error': f'Error fetching competitor analysis: {str(e)}',
                'success': False
            }


def get_google_business_insights(access_token, location_name, start_date=None, end_date=None):
    """
    Helper function to get Google Business insights
    """
    analytics = GoogleBusinessAnalytics(access_token)
    return analytics.get_location_insights(location_name, start_date, end_date)


def get_google_business_performance(access_token, location_name, metric_type='all', start_date=None, end_date=None):
    """
    Helper function to get Google Business performance metrics
    """
    analytics = GoogleBusinessAnalytics(access_token)
    return analytics.get_performance_metrics(location_name, metric_type, start_date, end_date)


def get_google_business_reviews(access_token, location_name):
    """
    Helper function to get Google Business reviews summary
    """
    analytics = GoogleBusinessAnalytics(access_token)
    return analytics.get_reviews_summary(location_name)


def get_google_business_photos(access_token, location_name):
    """
    Helper function to get Google Business photo insights
    """
    analytics = GoogleBusinessAnalytics(access_token)
    return analytics.get_photo_insights(location_name)
