import requests
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from datetime import datetime, timedelta
import json

class GoogleBusinessProfile:
    def __init__(self, access_token):
        self.access_token = access_token
        self.credentials = Credentials(token=access_token)
        self.business_service = None
        self.account_management_service = None
        
        try:
            # Build services
            self.business_service = build('mybusinessbusinessinformation', 'v1', credentials=self.credentials)
            self.account_management_service = build('mybusinessaccountmanagement', 'v1', credentials=self.credentials)
        except Exception as e:
            print(f"Error building services: {str(e)}")
    
    def get_accounts(self):
        """
        Get all Google My Business accounts
        """
        try:
            if not self.account_management_service:
                return {'error': 'Account management service not available', 'success': False}
            
            result = self.account_management_service.accounts().list().execute()
            accounts = result.get('accounts', [])
            
            formatted_accounts = []
            for account in accounts:
                formatted_accounts.append({
                    'account_id': account.get('name', '').split('/')[-1],
                    'account_name': account.get('accountName', ''),
                    'type': account.get('type', ''),
                    'role': account.get('role', ''),
                    'state': account.get('state', {}).get('status', ''),
                    'verification_state': account.get('verificationState', ''),
                    'full_name': account.get('name', '')
                })
            
            return {
                'accounts': formatted_accounts,
                'total_accounts': len(formatted_accounts),
                'success': True
            }
            
        except HttpError as e:
            return {
                'error': f'HTTP Error: {str(e)}',
                'success': False
            }
        except Exception as e:
            return {
                'error': f'Error fetching accounts: {str(e)}',
                'success': False
            }
    
    def get_locations(self, account_id=None):
        """
        Get all business locations for an account
        """
        try:
            if not self.business_service:
                return {'error': 'Business service not available', 'success': False}
            
            # If no account_id provided, get first account
            if not account_id:
                accounts_result = self.get_accounts()
                if not accounts_result.get('success') or not accounts_result.get('accounts'):
                    return {'error': 'No accounts found', 'success': False}
                account_id = accounts_result['accounts'][0]['account_id']
            
            parent = f"accounts/{account_id}"
            result = self.business_service.accounts().locations().list(parent=parent).execute()
            locations = result.get('locations', [])
            
            formatted_locations = []
            for location in locations:
                profile = location.get('profile', {})
                address = location.get('storefrontAddress', {})
                
                formatted_locations.append({
                    'location_id': location.get('name', '').split('/')[-1],
                    'location_name': profile.get('description', ''),
                    'business_name': location.get('title', ''),
                    'address': {
                        'street': address.get('addressLines', [''])[0] if address.get('addressLines') else '',
                        'city': address.get('locality', ''),
                        'state': address.get('administrativeArea', ''),
                        'postal_code': address.get('postalCode', ''),
                        'country': address.get('regionCode', '')
                    },
                    'phone_number': location.get('phoneNumbers', {}).get('primaryPhone', ''),
                    'website_url': location.get('websiteUri', ''),
                    'categories': [cat.get('displayName', '') for cat in location.get('categories', {}).get('primaryCategory', [])],
                    'verification_state': location.get('metadata', {}).get('verificationState', ''),
                    'visibility_state': location.get('metadata', {}).get('visibilityState', ''),
                    'full_name': location.get('name', ''),
                    'latitude': location.get('latlng', {}).get('latitude'),
                    'longitude': location.get('latlng', {}).get('longitude')
                })
            
            return {
                'locations': formatted_locations,
                'total_locations': len(formatted_locations),
                'account_id': account_id,
                'success': True
            }
            
        except HttpError as e:
            return {
                'error': f'HTTP Error: {str(e)}',
                'success': False
            }
        except Exception as e:
            return {
                'error': f'Error fetching locations: {str(e)}',
                'success': False
            }
    
    def get_location_details(self, location_name):
        """
        Get detailed information for a specific location
        """
        try:
            if not self.business_service:
                return {'error': 'Business service not available', 'success': False}
            
            result = self.business_service.locations().get(name=location_name).execute()
            
            profile = result.get('profile', {})
            address = result.get('storefrontAddress', {})
            metadata = result.get('metadata', {})
            
            location_details = {
                'location_id': result.get('name', '').split('/')[-1],
                'location_name': profile.get('description', ''),
                'business_name': result.get('title', ''),
                'address': {
                    'street': address.get('addressLines', [''])[0] if address.get('addressLines') else '',
                    'city': address.get('locality', ''),
                    'state': address.get('administrativeArea', ''),
                    'postal_code': address.get('postalCode', ''),
                    'country': address.get('regionCode', '')
                },
                'phone_number': result.get('phoneNumbers', {}).get('primaryPhone', ''),
                'website_url': result.get('websiteUri', ''),
                'categories': [cat.get('displayName', '') for cat in result.get('categories', {}).get('primaryCategory', [])],
                'verification_state': metadata.get('verificationState', ''),
                'visibility_state': metadata.get('visibilityState', ''),
                'business_hours': result.get('regularHours', {}),
                'special_hours': result.get('specialHours', []),
                'attributes': result.get('attributes', []),
                'photos': result.get('photos', []),
                'full_name': result.get('name', ''),
                'latitude': result.get('latlng', {}).get('latitude'),
                'longitude': result.get('latlng', {}).get('longitude'),
                'place_id': result.get('metadata', {}).get('placeId', ''),
                'google_updated': result.get('metadata', {}).get('googleUpdated', ''),
                'merchant_updated': result.get('metadata', {}).get('merchantUpdated', '')
            }
            
            return {
                'location': location_details,
                'success': True
            }
            
        except HttpError as e:
            return {
                'error': f'HTTP Error: {str(e)}',
                'success': False
            }
        except Exception as e:
            return {
                'error': f'Error fetching location details: {str(e)}',
                'success': False
            }


def get_google_business_accounts(access_token):
    """
    Helper function to get Google Business accounts
    """
    profile = GoogleBusinessProfile(access_token)
    return profile.get_accounts()


def get_google_business_locations(access_token, account_id=None):
    """
    Helper function to get Google Business locations
    """
    profile = GoogleBusinessProfile(access_token)
    return profile.get_locations(account_id)


def get_google_business_location_details(access_token, location_name):
    """
    Helper function to get specific location details
    """
    profile = GoogleBusinessProfile(access_token)
    return profile.get_location_details(location_name)
