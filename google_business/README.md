# Google Business Profile Integration

This module provides comprehensive Google Business Profile (formerly Google My Business) integration similar to Metricool's GMB dashboard functionality.

## Project Configuration
- **Project ID**: zeta-verbena-443804-q5
- **Project Number**: ************

## Features

### 1. Authentication
- OAuth 2.0 flow for Google My Business API
- Token refresh functionality
- Secure credential management

### 2. Business Profile Data
- Get all business accounts
- Retrieve business locations
- Fetch detailed location information
- Business hours, contact info, categories

### 3. Analytics & Insights (Similar to Metricool)
- Profile views and customer actions
- Search queries and discovery metrics
- Photo performance insights
- Reviews summary and ratings
- Performance trends and growth metrics

## API Endpoints

### Authentication Endpoints

#### 1. Get Authorization URL
```
POST /api/google-business/auth-url/
```
**Request Body:**
```json
{
    "client_id": "your_google_client_id",
    "client_secret": "your_google_client_secret"
}
```

**Response:**
```json
{
    "authorization_url": "https://accounts.google.com/o/oauth2/auth?...",
    "state": "random_state_string",
    "success": true
}
```

#### 2. Exchange Code for Token
```
POST /api/google-business/callback/
```
**Request Body:**
```json
{
    "code": "authorization_code_from_google",
    "client_id": "your_google_client_id",
    "client_secret": "your_google_client_secret",
    "state": "state_from_auth_url"
}
```

**Response:**
```json
{
    "access_token": "ya29.a0AfH6SMC...",
    "refresh_token": "1//04...",
    "expires_in": 3599,
    "token_type": "Bearer",
    "scope": "https://www.googleapis.com/auth/business.manage",
    "success": true
}
```

#### 3. Refresh Access Token
```
POST /api/google-business/refresh-token/
```
**Request Body:**
```json
{
    "refresh_token": "1//04...",
    "client_id": "your_google_client_id",
    "client_secret": "your_google_client_secret"
}
```

### Profile Data Endpoints

#### 4. Get Business Accounts
```
POST /api/google-business/accounts/
```
**Request Body:**
```json
{
    "access_token": "ya29.a0AfH6SMC..."
}
```

**Response:**
```json
{
    "accounts": [
        {
            "account_id": "*********",
            "account_name": "My Business Account",
            "type": "PERSONAL",
            "role": "OWNER",
            "state": "VERIFIED",
            "verification_state": "VERIFIED",
            "full_name": "accounts/*********"
        }
    ],
    "total_accounts": 1,
    "success": true
}
```

#### 5. Get Business Locations
```
POST /api/google-business/locations/
```
**Request Body:**
```json
{
    "access_token": "ya29.a0AfH6SMC...",
    "account_id": "*********"  // Optional
}
```

**Response:**
```json
{
    "locations": [
        {
            "location_id": "*********",
            "location_name": "My Business Location",
            "business_name": "My Business",
            "address": {
                "street": "123 Main St",
                "city": "New York",
                "state": "NY",
                "postal_code": "10001",
                "country": "US"
            },
            "phone_number": "******-123-4567",
            "website_url": "https://mybusiness.com",
            "categories": ["Restaurant"],
            "verification_state": "VERIFIED",
            "visibility_state": "PUBLISHED",
            "latitude": 40.7128,
            "longitude": -74.0060
        }
    ],
    "total_locations": 1,
    "success": true
}
```

#### 6. Get Location Details
```
POST /api/google-business/location-details/
```
**Request Body:**
```json
{
    "access_token": "ya29.a0AfH6SMC...",
    "location_name": "accounts/*********/locations/*********"
}
```

### Analytics Endpoints (Metricool-style Data)

#### 7. Get Business Insights
```
POST /api/google-business/insights/
```
**Request Body:**
```json
{
    "access_token": "ya29.a0AfH6SMC...",
    "location_name": "accounts/*********/locations/*********",
    "start_date": "2024-01-01",  // Optional
    "end_date": "2024-01-31"     // Optional
}
```

**Response:**
```json
{
    "location_name": "accounts/*********/locations/*********",
    "date_range": {
        "start_date": "2024-01-01",
        "end_date": "2024-01-31"
    },
    "metrics": {
        "views": {
            "search_views": 1250,
            "maps_views": 850,
            "total_views": 2100
        },
        "actions": {
            "website_clicks": 45,
            "phone_calls": 23,
            "direction_requests": 67,
            "total_actions": 135
        },
        "discovery": {
            "direct_searches": 800,
            "discovery_searches": 1200,
            "branded_searches": 100
        },
        "photos": {
            "merchant_photos": 15,
            "customer_photos": 8,
            "photo_views": 450
        }
    },
    "success": true
}
```

#### 8. Get Performance Metrics
```
POST /api/google-business/performance/
```
**Request Body:**
```json
{
    "access_token": "ya29.a0AfH6SMC...",
    "location_name": "accounts/*********/locations/*********",
    "metric_type": "all",        // Optional: 'all', 'views', 'actions'
    "start_date": "2024-01-01",  // Optional
    "end_date": "2024-01-31"     // Optional
}
```

## Setup Instructions

### 1. Google Cloud Console Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project: `zeta-verbena-443804-q5`
3. Enable the following APIs:
   - Google My Business API
   - Google My Business Business Information API
   - Google My Business Account Management API

### 2. OAuth 2.0 Credentials
1. Go to "Credentials" in Google Cloud Console
2. Create OAuth 2.0 Client ID
3. Add authorized redirect URIs:
   - `http://localhost:8000/api/google-business/callback`
   - Your production domain callback URL

### 3. Required Scopes
- `https://www.googleapis.com/auth/business.manage`
- `https://www.googleapis.com/auth/plus.business.manage`

## Usage Example

```python
# 1. Get authorization URL
import requests

response = requests.post('http://localhost:8000/api/google-business/auth-url/', json={
    'client_id': 'your_client_id',
    'client_secret': 'your_client_secret'
})
auth_data = response.json()
print(f"Visit: {auth_data['authorization_url']}")

# 2. After user authorizes, exchange code for token
token_response = requests.post('http://localhost:8000/api/google-business/callback/', json={
    'code': 'code_from_callback',
    'client_id': 'your_client_id',
    'client_secret': 'your_client_secret',
    'state': auth_data['state']
})
tokens = token_response.json()

# 3. Get business locations
locations_response = requests.post('http://localhost:8000/api/google-business/locations/', json={
    'access_token': tokens['access_token']
})
locations = locations_response.json()

# 4. Get analytics data (Metricool-style)
insights_response = requests.post('http://localhost:8000/api/google-business/insights/', json={
    'access_token': tokens['access_token'],
    'location_name': locations['locations'][0]['full_name']
})
insights = insights_response.json()
```

## Notes

- Some analytics features require special access from Google
- The insights data structure matches Metricool's GMB dashboard format
- Token refresh should be implemented for long-term usage
- Rate limits apply to Google My Business API calls

## Dependencies Added
- `google-cloud-storage==2.10.0` (added to requirements.txt)
- Uses existing `google-api-python-client` and `google-auth` packages
