import os
import json
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import requests

class GoogleBusinessAuth:
    def __init__(self):
        self.project_id = "zeta-verbena-443804-q5"
        self.project_number = "192852807838"
        self.scopes = [
            'https://www.googleapis.com/auth/business.manage',
            'https://www.googleapis.com/auth/plus.business.manage'
        ]
        self.redirect_uri = 'http://localhost:8000/api/google-business/callback'
        
    def get_authorization_url(self, client_id, client_secret):
        """
        Generate Google OAuth authorization URL for Google My Business
        """
        try:
            # Create client config
            client_config = {
                "web": {
                    "client_id": client_id,
                    "client_secret": client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [self.redirect_uri]
                }
            }
            
            flow = Flow.from_client_config(
                client_config,
                scopes=self.scopes,
                redirect_uri=self.redirect_uri
            )
            
            authorization_url, state = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true',
                prompt='consent'
            )
            
            return {
                'authorization_url': authorization_url,
                'state': state,
                'success': True
            }
            
        except Exception as e:
            return {
                'error': f'Failed to generate authorization URL: {str(e)}',
                'success': False
            }
    
    def exchange_code_for_token(self, code, client_id, client_secret, state=None):
        """
        Exchange authorization code for access token
        """
        try:
            client_config = {
                "web": {
                    "client_id": client_id,
                    "client_secret": client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [self.redirect_uri]
                }
            }
            
            flow = Flow.from_client_config(
                client_config,
                scopes=self.scopes,
                redirect_uri=self.redirect_uri,
                state=state
            )
            
            flow.fetch_token(code=code)
            credentials = flow.credentials
            
            return {
                'access_token': credentials.token,
                'refresh_token': credentials.refresh_token,
                'expires_in': credentials.expiry.timestamp() if credentials.expiry else None,
                'token_type': 'Bearer',
                'scope': ' '.join(self.scopes),
                'success': True
            }
            
        except Exception as e:
            return {
                'error': f'Failed to exchange code for token: {str(e)}',
                'success': False
            }
    
    def refresh_access_token(self, refresh_token, client_id, client_secret):
        """
        Refresh access token using refresh token
        """
        try:
            credentials = Credentials(
                token=None,
                refresh_token=refresh_token,
                token_uri="https://oauth2.googleapis.com/token",
                client_id=client_id,
                client_secret=client_secret,
                scopes=self.scopes
            )
            
            credentials.refresh(Request())
            
            return {
                'access_token': credentials.token,
                'refresh_token': credentials.refresh_token,
                'expires_in': credentials.expiry.timestamp() if credentials.expiry else None,
                'token_type': 'Bearer',
                'success': True
            }
            
        except Exception as e:
            return {
                'error': f'Failed to refresh token: {str(e)}',
                'success': False
            }
    
    def validate_token(self, access_token):
        """
        Validate Google access token
        """
        try:
            response = requests.get(
                f'https://www.googleapis.com/oauth2/v1/tokeninfo?access_token={access_token}'
            )
            
            if response.status_code == 200:
                token_info = response.json()
                return {
                    'valid': True,
                    'expires_in': token_info.get('expires_in'),
                    'scope': token_info.get('scope'),
                    'success': True
                }
            else:
                return {
                    'valid': False,
                    'error': 'Invalid token',
                    'success': False
                }
                
        except Exception as e:
            return {
                'valid': False,
                'error': f'Token validation failed: {str(e)}',
                'success': False
            }
    
    def build_service(self, access_token):
        """
        Build Google My Business API service
        """
        try:
            credentials = Credentials(token=access_token)
            service = build('mybusinessbusinessinformation', 'v1', credentials=credentials)
            return service
        except Exception as e:
            print(f"Error building service: {str(e)}")
            return None


def get_google_business_auth_url(client_id, client_secret):
    """
    Helper function to get Google Business authorization URL
    """
    auth = GoogleBusinessAuth()
    return auth.get_authorization_url(client_id, client_secret)


def exchange_google_business_code(code, client_id, client_secret, state=None):
    """
    Helper function to exchange authorization code for tokens
    """
    auth = GoogleBusinessAuth()
    return auth.exchange_code_for_token(code, client_id, client_secret, state)


def refresh_google_business_token(refresh_token, client_id, client_secret):
    """
    Helper function to refresh Google Business access token
    """
    auth = GoogleBusinessAuth()
    return auth.refresh_access_token(refresh_token, client_id, client_secret)
